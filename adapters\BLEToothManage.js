/**
 * 德佟SDK BLEToothManage适配层
 * 适配原有SUPVANAPIT50PRO BLEToothManage接口到德佟SDK
 */

import bleTool from './BLETool.js';

class BLEToothManage {
  constructor() {
    this.lpapi = null;
  }

  /**
   * 获取LPAPI实例
   */
  async getLPAPI() {
    if (!this.lpapi) {
      this.lpapi = await bleTool.initLPAPI();
    }
    return this.lpapi;
  }

  /**
   * 获取耗材信息
   * 注意：德佟P2打印机不支持获取耗材信息，这里返回默认兼容信息
   * @returns {Promise}
   */
  async ConsumableInformation() {
    try {
      console.log('德佟P2打印机不支持获取耗材信息，返回默认兼容信息');
      
      // 返回默认的兼容耗材信息
      const defaultMaterialInfo = {
        width: 40,  // 默认宽度40mm
        height: 30, // 默认高度30mm
        gap: 2,     // 默认间隙2mm
        type: 'label', // 标签类型
        compatible: true // 标记为兼容
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultMaterialInfo,
        message: '获取耗材信息成功（默认兼容）'
      });
    } catch (error) {
      console.error('获取耗材信息异常:', error);
      return Promise.reject({
        ResultCode: 129,
        message: '未检测到耗材',
        error
      });
    }
  }

  /**
   * 绘制预览图
   * @param {Object} canvas Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doDrawPreview(canvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();
      
      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];
      
      // 德佟SDK预览绘制（简化实现）
      console.log('德佟SDK绘制预览，模板:', template);

      return new Promise((resolve, reject) => {
        try {
          // 创建预览任务 - 使用德佟SDK规定的预览jobName格式
          const jobResult = lpapi.startJob({
            width: template.Width || 40,
            height: template.Height || 30,
            orientation: template.Rotate || 0,
            jobName: "#!#preview#!#"  // 德佟SDK预览模式专用jobName
          });

          if (!jobResult) {
            throw new Error('预览任务创建失败');
          }

          // 绘制模板内容 - 参考index页面textPrintTest2的参数
          if (template.DrawObjects && Array.isArray(template.DrawObjects)) {
            template.DrawObjects.forEach(item => {
              if (item.Format === 'TEXT' || !item.Format) {
                // 确保FontSize是数字格式
                const fontSize = typeof item.FontSize === 'string' ?
                  parseFloat(item.FontSize) : (item.FontSize || 4);

                lpapi.drawText({
                  text: item.Content || '',
                  x: item.X || 0,
                  y: item.Y || 0,
                  width: item.Width || 10,
                  // height: item.Height,  // 不设置height，让SDK自动计算
                  fontHeight: fontSize,
                  fontName: item.FontName || '',
                  fontStyle: item.FontStyle || 0,
                  // 关键参数 - 参考index页面
                  autoReturn: 1,
                  lineSpace: 0,
                  charSpace: 0
                });
              }
            });
          }

          // 提交预览任务 - 参考index页面的参数
          lpapi.commitJob({
            gapType: 2, // 间隙纸
            darkness: template.Density || 2,
            printSpeed: template.Speed || 25
          }).then((resp) => {
            console.log('德佟SDK预览成功:', resp);
            if (resp.statusCode === 0) {
              // 缓存预览图和模板，用于后续打印优化
              if (resp.dataUrls && resp.dataUrls.length > 0) {
                this.cachedPreviewUrl = resp.dataUrls[0];
                this.cachedTemplate = JSON.parse(JSON.stringify(template)); // 深拷贝
                console.log('德佟P2缓存预览图:', this.cachedPreviewUrl);
              }

              const successResult = {
                ResultCode: 0,
                message: '预览生成成功',
                data: resp,
                previewData: resp.dataUrls || []
              };
              if (callback) callback(successResult);
              resolve(successResult);
            } else {
              throw new Error(`预览失败，statusCode: ${resp.statusCode}`);
            }
          }).catch(error => {
            console.error('德佟SDK预览提交失败:', error);
            const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
            if (callback) callback(errorResult);
            reject(errorResult);
          });

        } catch (error) {
          console.error('德佟SDK预览绘制失败:', error);
          const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
          if (callback) callback(errorResult);
          reject(errorResult);
        }
      });
    } catch (error) {
      console.error('绘制预览异常:', error);
      const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 执行打印
   * @param {Object} textCanvas 文本Canvas对象
   * @param {Array} templates 模板数组
   * @param {Object} barcodeCanvas 条码Canvas对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async doPrintMatrix(textCanvas, templates, barcodeCanvas, callback) {
    try {
      const lpapi = await this.getLPAPI();

      if (!templates || templates.length === 0) {
        const error = { ResultCode: 116, message: '模板对象不能为空' };
        if (callback) callback(error);
        throw error;
      }

      const template = templates[0];

      // 检查是否已连接设备
      if (!bleTool.isConnected()) {
        const error = { ResultCode: 109, message: '设备未连接' };
        if (callback) callback(error);
        throw error;
      }

      console.log('德佟P2开始打印，模板:', template);

      // 优化：检查是否有缓存的预览图可以直接打印
      if (this.cachedPreviewUrl && this.cachedTemplate &&
          this.isSameTemplate(template, this.cachedTemplate)) {
        console.log('德佟P2使用缓存的预览图直接打印');
        return this.printWithCachedImage(this.cachedPreviewUrl, template, callback);
      } else {
        // 生成新的预览图并打印
        return this.printWithPreviewImage(template, callback);
      }
    } catch (error) {
      console.error('德佟P2打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 比较两个模板是否相同
   * @param {Object} template1 模板1
   * @param {Object} template2 模板2
   * @returns {boolean}
   */
  isSameTemplate(template1, template2) {
    if (!template1 || !template2) return false;

    // 比较关键属性
    const keys = ['Width', 'Height', 'Density', 'Speed'];
    for (const key of keys) {
      if (template1[key] !== template2[key]) return false;
    }

    // 比较DrawObjects内容
    if (!template1.DrawObjects || !template2.DrawObjects) return false;
    if (template1.DrawObjects.length !== template2.DrawObjects.length) return false;

    for (let i = 0; i < template1.DrawObjects.length; i++) {
      const obj1 = template1.DrawObjects[i];
      const obj2 = template2.DrawObjects[i];
      if (obj1.Content !== obj2.Content || obj1.X !== obj2.X || obj1.Y !== obj2.Y) {
        return false;
      }
    }

    return true;
  }

  /**
   * 使用缓存图片直接打印
   * @param {string} imageUrl 图片URL
   * @param {Object} template 模板对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async printWithCachedImage(imageUrl, template, callback) {
    try {
      const lpapi = await this.getLPAPI();

      return new Promise((resolve, reject) => {
        lpapi.printImage({
          image: imageUrl, // 修复：根据README文档，参数应该是image而不是imageUrl
          copies: template.Copies || 1,
          gapType: 2,
          printDarkness: template.Density || 2, // 修复：根据README文档，参数应该是printDarkness
          printSpeed: template.Speed || 25,
          success: (result) => {
            console.log('德佟P2缓存图片打印成功:', result);
            const successResult = { ResultCode: 0, message: '打印成功', data: result };
            if (callback) callback(successResult);
            resolve(successResult);
          },
          fail: (error) => {
            console.error('德佟P2缓存图片打印失败:', error);
            this.handlePrintError(error, callback, reject);
          },
          complete: (result) => {
            console.log('德佟P2缓存图片打印完成:', result);
          }
        });
      });
    } catch (error) {
      console.error('德佟P2缓存图片打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 通过预览图进行打印
   * @param {Object} template 模板对象
   * @param {Function} callback 回调函数
   * @returns {Promise}
   */
  async printWithPreviewImage(template, callback) {
    try {
      const lpapi = await this.getLPAPI();

      console.log('德佟P2：先生成预览图，再打印');

      return new Promise((resolve, reject) => {
        try {
          // 第一步：创建预览任务 - 先生成图片
          const jobResult = lpapi.startJob({
            width: template.Width || 40,
            height: template.Height || 30,
            orientation: template.Rotate || 0,
            jobName: "#!#preview#!#"  // 先用预览模式生成图片
          });

          if (!jobResult) {
            throw new Error('打印任务创建失败');
          }

          // 第二步：绘制模板内容 - 参考index页面textPrintTest2的参数
          if (template.DrawObjects && Array.isArray(template.DrawObjects)) {
            template.DrawObjects.forEach(item => {
              if (item.Format === 'TEXT' || !item.Format) {
                // 确保FontSize是数字格式
                const fontSize = typeof item.FontSize === 'string' ?
                  parseFloat(item.FontSize) : (item.FontSize || 4);

                lpapi.drawText({
                  text: item.Content || '',
                  x: item.X || 0,
                  y: item.Y || 0,
                  width: item.Width || 10,
                  // height: item.Height,  // 不设置height，让SDK自动计算
                  fontHeight: fontSize,
                  fontName: item.FontName || '',
                  fontStyle: item.FontStyle || 0,
                  // 关键参数 - 参考index页面
                  autoReturn: 1,
                  lineSpace: 0,
                  charSpace: 0
                });
              }
            });
          }

          // 第三步：提交任务生成图片 - 参考index页面的参数
          lpapi.commitJob({
            gapType: 2, // 间隙纸
            darkness: template.Density || 2,
            printSpeed: template.Speed || 25
          }).then((resp) => {
            console.log('德佟P2图片生成成功:', resp);

            if (resp.statusCode === 0 && resp.dataUrls && resp.dataUrls.length > 0) {
              // 第四步：使用printImage打印生成的图片
              const imageUrl = resp.dataUrls[0];

              lpapi.printImage({
                image: imageUrl, // 修复：根据README文档，参数应该是image而不是imageUrl
                copies: template.Copies || 1,
                gapType: 2,
                printDarkness: template.Density || 2, // 修复：根据README文档，参数应该是printDarkness
                printSpeed: template.Speed || 25,
                success: (printResp) => {
                  console.log('德佟P2打印成功:', printResp);
                  const successResult = {
                    ResultCode: 0,
                    message: '打印成功',
                    data: printResp
                  };
                  if (callback) callback(successResult);
                  resolve(successResult);
                },
                fail: (printError) => {
                  console.error('德佟P2打印失败:', printError);
                  this.handlePrintError(printError, callback, reject);
                },
                complete: (result) => {
                  console.log('德佟P2打印完成:', result);
                }
              });

            } else {
              throw new Error(`图片生成失败，statusCode: ${resp.statusCode}`);
            }
          }).catch(error => {
            console.error('德佟P2图片生成失败:', error);
            const errorResult = { ResultCode: 119, message: '生成图片失败异常', error };
            if (callback) callback(errorResult);
            reject(errorResult);
          });

        } catch (error) {
          console.error('德佟P2打印任务异常:', error);
          const errorResult = { ResultCode: 132, message: '打印异常终止', error };
          if (callback) callback(errorResult);
          reject(errorResult);
        }
      });
    } catch (error) {
      console.error('德佟P2打印异常:', error);
      const errorResult = { ResultCode: 132, message: '打印异常终止', error };
      if (callback) callback(errorResult);
      throw errorResult;
    }
  }

  /**
   * 处理打印错误
   * @param {Object} error 错误对象
   * @param {Function} callback 回调函数
   * @param {Function} reject Promise reject函数
   */
  handlePrintError(error, callback, reject) {
    console.log('德佟P2打印错误详情:', error);

    // 根据错误类型返回相应的错误码
    let resultCode = 132; // 默认打印异常终止
    let message = '打印异常终止';
    let shouldDisconnect = false; // 是否需要断开连接

    if (error.message && error.message.includes('耗材')) {
      if (error.message.includes('仓盖')) {
        resultCode = 126;
        message = '请关闭耗材仓盖';
      } else if (error.message.includes('未装好')) {
        resultCode = 127;
        message = '耗材未装好';
      } else if (error.message.includes('余量')) {
        resultCode = 128;
        message = '请检查耗材余量';
      } else if (error.message.includes('未检测')) {
        resultCode = 129;
        message = '未检测到耗材';
      } else if (error.message.includes('未识别')) {
        resultCode = 130;
        message = '未识别到耗材';
      } else if (error.message.includes('用完')) {
        resultCode = 131;
        message = '耗材已用完';
      }
    } else if (error.message && error.message.includes('色带')) {
      resultCode = 133;
      message = '色带错误';
    } else if (error.message && (error.message.includes('连接') || error.message.includes('断开'))) {
      // 只有连接相关的错误才需要断开
      shouldDisconnect = true;
      resultCode = 109;
      message = '设备连接异常';
    }

    // 重要：打印错误不应该影响设备连接状态
    // 只有真正的连接错误才断开设备
    if (shouldDisconnect) {
      console.log('德佟P2检测到连接错误，断开设备');
      bleTool.disconnectBleDevice().catch(err => {
        console.error('断开设备失败:', err);
      });
    } else {
      console.log('德佟P2打印错误，但保持设备连接状态');
    }

    const errorResult = {
      ResultCode: resultCode,
      message,
      error,
      keepConnection: !shouldDisconnect // 标识是否保持连接
    };

    if (callback) callback(errorResult);
    reject(errorResult);
  }

  /**
   * 获取页面信息（德佟P2不支持，返回默认信息）
   * @returns {Promise}
   */
  async getPageInfos() {
    try {
      console.log('德佟P2打印机不支持获取页面信息，返回默认信息');
      
      const defaultPageInfo = {
        width: 40,
        height: 30,
        gap: 2,
        supported: false // 标记为不支持
      };

      return Promise.resolve({
        ResultCode: 0,
        ResultValue: defaultPageInfo,
        message: '获取页面信息成功（默认信息）'
      });
    } catch (error) {
      console.error('获取页面信息异常:', error);
      return Promise.reject({
        ResultCode: 124,
        message: '生成图片数据异常',
        error
      });
    }
  }
}

// 创建单例实例
const bleToothManage = new BLEToothManage();

export default bleToothManage;
